<template>
  <a-modal
    v-model:open="dialog.visible"
    :title="!!detail ? '编辑' : '新建'"
    width="50%"
    @cancel="dialog.cancel"
  >
    <a-form
      ref="formRef"
      :model="info"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="模型名称" name="modelName" required>
        <a-input v-model:value="info.modelName" placeholder="请输入模型名称" />
      </a-form-item>
      <a-form-item label="专业软件" name="desProfessionalSoftwareId" required>
        <a-select
          v-model:value="info.desProfessionalSoftwareId"
          :options="
            modelStore.software.map((item: any) => ({
              label: item.softwareName,
              value: item.desProfessionalSoftwareId,
            }))
          "
          @change="(_, option: any) => (info.softwareCode = option.label)"
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="info.remark"
          :rows="4"
          placeholder="请输入备注"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <div>
        <a-button @click="dialog.cancel">取消</a-button>
        <a-button type="primary" @click="handldSubmit">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { addModel, updateModal } from '@/master/apis/model-tool';
import { ModelRecord } from '@/master/types/model';
import { message } from 'ant-design-vue';
import { useDialog } from 'dialog-async';
import useModelStore from '@/master/stores/model';

const { detail } = defineProps<{
  detail?: ModelRecord;
}>();

const dialog = useDialog();

const modelStore = useModelStore();

const formRef = ref();

const info = ref<ModelRecord>(detail ?? ({} as ModelRecord));

const handldSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      if (!detail) {
        await addModel(info.value);
        message.success('新建成功');
      } else {
        await updateModal(info.value);
        message.success('修改成功');
      }
      dialog.submit();
    })
    .catch((error: any) => {
      console.log('error', error);
    });
};
</script>
