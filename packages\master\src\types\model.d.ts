export interface ModelRecord extends Global.Entity {
  desProfessionalSoftwareId: string;
  modelName: string;
  modelPath: string;
  modelVersion: string;
  softwareCode: string;
  sysDesModelId: string;
  remark: string;
}

export interface ModelData extends Global.Entity {
  commandCode: string;
  dataCode: string;
  dataName: string;
  dataUnit?: string;
  dataValue?: string;
  enumCode: string;
  parentId: string;
  sysDesModelDataId: string;
  sysDesModelId: string;
  unitEnum?: string;
  valueEnum?: string;
  parameterType: string;
}
