<template>
  <a-table
    bordered
    :data-source="data?.records"
    :columns="tableColumns"
    :scroll="{ y: tableHeight, x: 1400 }"
    :loading
    size="small"
    :pagination="false"
  />
  <div class="flex justify-end mt-2">
    <a-pagination
      v-model:current="pagination.pageNum"
      v-model:pageSize="pagination.pageSize"
      show-size-changer
      :total="pagination.total"
      @change="onShowSizeChange"
    />
  </div>
</template>

<script setup lang="tsx">
import { message, TableColumnsType } from 'ant-design-vue';
import {
  getToolListByPage,
  updateToolStatus,
  updateTool,
} from '@/master/apis/tools';
import { showDialog } from 'dialog-async';
import dayjs from 'dayjs';
import AddBasicComponentModal from './AddBasicComponentModal.vue';
import { useRequest } from 'vue-hooks-plus';
import { Tool } from '@/master/types/tool';
import { ToolType } from '../workbench/constants';

const router = useRouter();

const pagination = ref({
  total: 0,
  pageNum: 1,
  pageSize: 20,
});

const tableHeight = computed(() => document.documentElement.clientHeight - 125);

const tableColumns: TableColumnsType<Tool> = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    fixed: 'left',
    align: 'center',
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '组件名称',
    dataIndex: 'componentName',
    key: 'componentName',
    align: 'center',
    minWidth: 140,
    fixed: 'left',
  },
  {
    title: '组件状态',
    dataIndex: 'componentStatus',
    key: 'componentStatus',
    align: 'center',
    customRender: ({ record }) => (
      <a-tag color={record.componentStatus === '1' ? 'success' : 'error'}>
        {record.componentStatus === '1' ? '已发布' : '未发布'}
      </a-tag>
    ),
    minWidth: 100,
  },
  {
    title: '组件类型',
    dataIndex: 'componentType',
    key: 'componentType',
    align: 'center',
    minWidth: 100,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    key: 'createUser',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    key: 'createTime',
    minWidth: 140,
    customRender: ({ value }) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '更新人',
    dataIndex: 'updateUser',
    key: 'updateUser',
    align: 'center',
    minWidth: 120,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    align: 'center',
    key: 'updateTime',
    minWidth: 140,
    customRender: ({ value }) => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    key: 'action',
    minWidth: 220,
    fixed: 'right',
    align: 'center',
    customRender: ({ record }) => (
      <a-space size="small">
        <a-button type="link" onClick={() => handleEdit(record)}>
          {{ default: () => '编辑' }}
        </a-button>
        <a-popconfirm
          title="确定删除吗？"
          okText="确定"
          cancelText="取消"
          onConfirm={() => handleDelete(record)}
        >
          <a-button type="link" danger>
            {{ default: () => '删除' }}
          </a-button>
        </a-popconfirm>
        <a-button type="link" onClick={() => handleUpdateStatus(record)}>
          {{
            default: () =>
              record.componentStatus === '1' ? '取消发布' : '发布',
          }}
        </a-button>
      </a-space>
    ),
  },
];

const { data, loading, run } = useRequest(getToolListByPage, {
  defaultParams: [pagination.value],
  onSuccess: res => {
    pagination.value = {
      total: res.total,
      pageSize: res.size,
      pageNum: res.current,
    };
  },
});

const refresh = () => {
  run({
    pageNum: pagination.value.pageNum,
    pageSize: pagination.value.pageSize,
  });
};

const handleEdit = async (row: Tool) => {
  if (row.componentType === ToolType.SOFT) {
    router.push({
      path: '/workbench',
      query: {
        sysDesComponentId: row.sysDesComponentId,
        professionalSoftwareId: row.desProfessionalSoftwareId,
      },
    });
  } else {
    showDialog(<AddBasicComponentModal />).catch(() => {});
  }
};

const handleDelete = async (row: Tool) => {
  const res = await updateTool({
    sysDesComponentId: row.sysDesComponentId,
    bsflag: 'Y',
  });
  if (res) {
    message.success('删除成功');
    refresh();
  }
};

const onShowSizeChange = (current: number, pageSize: number) => {
  pagination.value.pageNum = current;
  pagination.value.pageSize = pageSize;
  refresh();
};

const handleUpdateStatus = async (record: Tool) => {
  try {
    await updateToolStatus({
      sysDesComponentId: record.sysDesComponentId,
      status: record.componentStatus === '1' ? '0' : '1',
    });
    message.success('操作成功');
    refresh();
  } catch (error) {
    message.error('操作失败');
  }
};
</script>
