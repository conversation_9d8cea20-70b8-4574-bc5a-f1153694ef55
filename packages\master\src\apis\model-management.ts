import { request } from '@kd/utils';
import type {
  SysDesModelAttachment,
  SysDesModelAttachmentQueryParams,
  SysDesModelServer,
  SysDesModelServerQueryParams,
  UploadModelImageResponse,
} from '../types/model-management';

/**
 * 分页查询模型版本控制表
 * @param params 分页参数
 * @param data 查询条件
 * @returns 分页数据
 */
export const listModelAttachmentPage = (
  data: SysDesModelAttachmentQueryParams
) =>
  request<SysDesModelAttachment[]>({
    url: `/component/sysDesModelAttachment/queryList`,
    method: 'POST',
    data: {
      ...data,
      bsflag: 'N',
    },
  });

/**
 * 根据ID查询模型版本控制表
 * @param id 主键ID
 * @returns 模型版本控制表详情
 */
export const getModelAttachmentById = (id: string) =>
  request<SysDesModelAttachment>({
    url: '/component/sysDesModelAttachment/queryById',
    method: 'GET',
    params: { id },
  });

/**
 * 新增模型版本控制表
 * @param data 模型版本控制表数据
 * @returns 新增结果
 */
export const addModelAttachment = (data: Partial<SysDesModelAttachment>) =>
  request<{ sysDesModelAttachmentId: string }>({
    url: '/component/sysDesModelAttachment/add',
    method: 'POST',
    data,
  });

/**
 * 修改模型版本控制表
 * @param data 模型版本控制表数据
 * @returns 修改结果
 */
export const updateModelAttachment = (data: Partial<SysDesModelAttachment>) =>
  request<boolean>({
    url: '/component/sysDesModelAttachment/update',
    method: 'POST',
    data,
  });

/**
 * 删除模型版本控制表
 * @param ids 主键ID数组
 * @returns 删除结果
 */
export const deleteModelAttachment = (ids: string[]) =>
  request<number>({
    url: '/component/sysDesModelAttachment/delete',
    method: 'GET',
    params: { idList: ids },
  });

/**
 * 上传模型图片
 * @param file 图片文件
 * @param sysDesModelAttachmentId 模型版本控制表ID
 * @returns 上传结果
 */
export const uploadModelImage = (
  file: File,
  sysDesModelAttachmentId: string
) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('sysDesModelAttachmentId', sysDesModelAttachmentId);

  return request<UploadModelImageResponse>({
    url: '/component/sysDesModelAttachment/uploadModelImage',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 上传单文件模型
 * @param file 模型文件
 * @param sysDesModelAttachmentId 模型版本控制表ID
 * @param sysDesModelServerId 专业软件服务器配置ID
 * @param fileVersion 版本号
 * @returns 上传结果
 */
export const uploadModel = (data: {
  file: File;
  sysDesModelAttachmentId: string;
  desProfessionalSoftwareId: string;
  fileVersion: string;
}) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('sysDesModelAttachmentId', data.sysDesModelAttachmentId);
  formData.append('desProfessionalSoftwareId', data.desProfessionalSoftwareId);
  formData.append('fileVersion', data.fileVersion);
  return request<string>({
    url: '/component/sysDesModelAttachment/uploadModel',
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 文件夹模型上传
 * @param file 模型文件夹
 * @param sysDesModelAttachmentId 模型版本控制表ID
 * @param folderName 文件夹名称
 * @param defaultModelPath 指定文件夹下的主模型路径
 * @param fileVersion 版本号
 * @returns 上传结果
 */
export const uploadModels = (
  file: File,
  sysDesModelAttachmentId: string,
  folderName: string,
  defaultModelPath: string,
  fileVersion: string
) => {
  const formData = new FormData();
  formData.append('file', file);

  return request<string>({
    url: `/component/sysDesModelAttachment/uploadModels?sysDesModelAttachmentId=${sysDesModelAttachmentId}&folderName=${folderName}&defaultModelPath=${defaultModelPath}&fileVersion=${fileVersion}`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 分页查询模型存储服务器信息
 * @param params 分页参数
 * @param data 查询条件
 * @returns 分页数据
 */
export const listModelServerPage = (
  params: Global.Pagination,
  data: SysDesModelServerQueryParams = {}
) =>
  request<Global.ListResponse<SysDesModelServer>>({
    url: `/component/sysDesModelServer/listPage?pageNum=${params.pageNum}&pageSize=${params.pageSize}`,
    method: 'POST',
    data,
  });

/**
 * 根据ID查询模型存储服务器信息
 * @param id 主键ID
 * @returns 模型存储服务器信息详情
 */
export const getModelServerById = (id: string) =>
  request<SysDesModelServer>({
    url: '/component/sysDesModelServer/queryById',
    method: 'GET',
    params: { id },
  });

/**
 * 新增模型存储服务器信息
 * @param data 模型存储服务器信息数据
 * @returns 新增结果
 */
export const addModelServer = (data: Partial<SysDesModelServer>) =>
  request<boolean>({
    url: '/component/sysDesModelServer/add',
    method: 'POST',
    data,
  });

/**
 * 修改模型存储服务器信息
 * @param data 模型存储服务器信息数据
 * @returns 修改结果
 */
export const updateModelServer = (data: Partial<SysDesModelServer>) =>
  request<boolean>({
    url: '/component/sysDesModelServer/update',
    method: 'POST',
    data,
  });

/**
 * 删除模型存储服务器信息
 * @param ids 主键ID数组
 * @returns 删除结果
 */
export const deleteModelServer = (ids: string[]) =>
  request<number>({
    url: '/component/sysDesModelServer/delete',
    method: 'GET',
    params: { idList: ids },
  });
