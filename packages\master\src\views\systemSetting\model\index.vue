<template>
  <div class="h-full">
    <a-card title="模型管理">
      <template #extra>
        <a-space>
          <a-button
            type="primary"
            :disabled="btnDisabled"
            @click="handleGetTreeData"
          >
            获取模型树
          </a-button>
          <a-button type="primary" @click="handleAdd">
            <template #icon>
              <GridiconsAdd />
            </template>
            新建模型
          </a-button>
          <a-button
            type="primary"
            ghost
            :disabled="btnDisabled"
            @click="handleAddModelFile"
          >
            <template #icon>
              <GridiconsAdd />
            </template>
            添加模型文件
          </a-button>
        </a-space>
      </template>
      <a-table
        :data-source="data?.records"
        :loading
        :style="{ height: `calc(100vh - 210px)`, overflowY: 'auto' }"
        :columns="tableColumns"
        :row-key="record => record.sysDesModelId"
        :row-selection="rowSelection"
        :pagination="false"
        size="small"
        @expand="handleExpand"
      >
        <template #expandedRowRender>
          <div class="p-4">
            <a-table
              bordered
              :data-source="modelVersionList"
              :loading="expandLoading"
              :columns="expandedColumns"
              :pagination="false"
              size="small"
            />
          </div>
        </template>
      </a-table>
      <div class="flex justify-end mt-2">
        <a-pagination
          v-model:current="pagination.current"
          v-model:pageSize="pagination.size"
          show-size-changer
          :total="pagination.total"
          @change="onShowSizeChange"
        />
      </div>
    </a-card>
  </div>
</template>

<script setup lang="tsx">
import { message, TableColumnsType, TableProps } from 'ant-design-vue';
import { queryByPage } from '@/master/apis/model-tool';
import { showDialog } from 'dialog-async';
import FormModal from './FormModal.vue';
import { deleteModal, requestModelTree } from '@/master/apis/model-tool';
import {
  deleteModelAttachment,
  listModelAttachmentPage,
} from '@/master/apis/model-management';
import dayjs from 'dayjs';
import { ModelRecord } from '@/master/types/model';
import { useRequest } from 'vue-hooks-plus';
import { SysDesModelAttachment } from '@/master/types/model-management';
import ModelFileModal from './ModelFileModal.vue';

const btnDisabled = computed(() => !selectedRow.value);

const pagination = ref({
  current: 1,
  size: 10,
  total: 0,
});

const tableColumns = computed<TableColumnsType>(() => [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    customRender: ({ index }: { index: number }) => index + 1,
  },
  { title: '模型名称', dataIndex: 'modelName', align: 'center' },
  {
    title: '专业软件',
    dataIndex: 'softwareCode',
    align: 'center',
    customRender: ({ value }) => <a-tag>{value}</a-tag>,
  },
  { title: '创建人', dataIndex: 'createUser', align: 'center' },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'center',
    customRender: opt =>
      opt.value ? dayjs(opt.value).format('YYYY-MM-DD HH:mm:ss') : '--',
  },
  { title: '更新人', dataIndex: 'updateUser', align: 'center' },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    align: 'center',
    customRender: opt =>
      opt.value ? dayjs(opt.value).format('YYYY-MM-DD HH:mm:ss') : '--',
  },
  { title: '描述', dataIndex: 'remark' },
  {
    title: '操作',
    key: 'action',
    width: 180,
    align: 'center',
    customRender: ({ record }: { record: any }) => (
      <div>
        <a-button type="link" onclick={() => handleEdit(record)}>
          编辑
        </a-button>
        <a-popconfirm
          title="确定删除吗？"
          ok-text="确定"
          cancel-text="取消"
          onConfirm={() => handleDel(record)}
        >
          <a-button type="link" danger>
            删除
          </a-button>
        </a-popconfirm>
      </div>
    ),
  },
]);

const expandedColumns: TableColumnsType<SysDesModelAttachment> = [
  {
    title: '序号',
    key: 'index',
    width: 80,
    align: 'center',
    customRender: ({ index }: { index: number }) => index + 1,
  },
  { title: '模型文件名称', dataIndex: 'modelName', align: 'center' },
  { title: '模型版本', dataIndex: 'modelVersion', align: 'center' },
  { title: '模型路径', dataIndex: 'modelPath', align: 'center' },
  {
    title: '模型截图',
    dataIndex: 'modelImage',
    align: 'center',
    customRender: ({ value }) => (
      <a-image src={value} height={32} alt="模型截图" />
    ),
  },
  { title: '备注', dataIndex: 'remark', align: 'center' },
  {
    title: '操作',
    key: 'action',
    width: 110,
    align: 'center',
    customRender: ({ record }) => (
      <a-popconfirm
        title="确定删除吗？"
        ok-text="确定"
        cancel-text="取消"
        onConfirm={() => handleDeleteModelFile(record)}
      >
        <a-button type="link" danger>
          删除
        </a-button>
      </a-popconfirm>
    ),
  },
];

const selectedRow = ref();

const onShowSizeChange = (current: number, pageSize: number) => {
  pagination.value.current = current;
  pagination.value.size = pageSize;
  refresh();
};

const rowSelection: TableProps['rowSelection'] = {
  columnWidth: 60,
  type: 'radio',
  onChange: (_, selectedRows) => {
    selectedRow.value = selectedRows[0];
  },
};

const { data, run, loading } = useRequest(queryByPage, {
  defaultParams: [
    {
      pageNum: pagination.value.current,
      pageSize: pagination.value.size,
      data: {},
    },
  ],
  onSuccess: res => {
    pagination.value.total = res.total;
  },
});

const {
  data: modelVersionList,
  run: getModelVersionRun,
  loading: expandLoading,
} = useRequest(listModelAttachmentPage, {
  manual: true,
});

const handleExpand = (expanded: boolean, record: ModelRecord) => {
  if (!expanded) return;
  getModelVersionRun({ sysDesModelId: record.sysDesModelId });
};

const refresh = () => {
  run({
    pageSize: pagination.value.size,
    pageNum: pagination.value.current,
    data: {},
  });
};

const handleAdd = () => {
  showDialog(h(<FormModal />))
    .then(refresh)
    .catch(() => {});
};

const handleEdit = (row: ModelRecord) => {
  showDialog(<FormModal detail={row} />)
    .then(refresh)
    .catch(() => {});
};

const handleAddModelFile = () => {
  showDialog(<ModelFileModal current={selectedRow.value} />)
    .then(() => {
      getModelVersionRun({ sysDesModelId: selectedRow.value.sysDesModelId });
    })
    .catch(() => {});
};

const handleDel = async (row: any) => {
  const res = await deleteModal(row.sysDesModelId);
  if (res.code === 200) {
    message.success('删除成功');
    refresh();
  } else {
    message.error('删除失败');
  }
};

const handleDeleteModelFile = async (record: SysDesModelAttachment) => {
  try {
    await deleteModelAttachment([record.sysDesModelAttachmentId]);
    message.success('删除成功');
    getModelVersionRun({ sysDesModelId: record.sysDesModelId });
  } catch (e: any) {
    message.error(e.message);
  }
};

const handleGetTreeData = async () => {
  requestModelTree(selectedRow.value.sysDesModelId).then(() => {
    message.success('获取成功');
  });
};
</script>
