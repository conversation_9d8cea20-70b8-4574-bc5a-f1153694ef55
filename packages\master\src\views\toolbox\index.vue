<template>
  <a-card title="工具库管理" class="h-full">
    <template #extra>
      <a-space>
        <MenuOutlined
          class="text-base"
          :class="toggleTable ? 'text-blue-500' : ''"
          @click="toggleTable = !toggleTable"
        />
        <MingcuteDotGridFill
          class="text-base"
          :class="!toggleTable ? 'text-blue-500' : ''"
          @click="toggleTable = !toggleTable"
        />
        <a-button type="primary" @click="handleAdd">
          <GridiconsAdd class="mr-1" />新增专业软件组件
        </a-button>
        <a-button type="primary" ghost @click="handleAddNormal">
          <GridiconsAdd class="mr-1" />新增通用组件
        </a-button>
      </a-space>
    </template>
    <TableList v-if="toggleTable" />
    <div v-else>
      <a-spin :spinning="loading">
        <CardList :data-source="data" @reload="refresh" />
      </a-spin>
    </div>
  </a-card>
</template>
<script lang="tsx" setup>
import CardList from './components/cardList.vue';
import { getToolCategory } from '@/master/apis/tools';
import { useRequest } from 'vue-hooks-plus';
import { showDialog } from 'dialog-async';
import AddBasicComponentModal from './components/AddBasicComponentModal.vue';
import AddSoftwareComponentModal from './components/AddSoftwareComponentModal.vue';
import MenuOutlined from '~icons/ant-design/menu-outlined';
import TableList from './components/TableList.vue';

const toggleTable = ref<boolean>(false);

const handleToggleTable = (val: boolean) => {
  if (toggleTable.value !== val) toggleTable.value = val;
};

const handleAdd = async () => {
  showDialog(<AddSoftwareComponentModal />).catch(() => {});
};

const handleAddNormal = async () => {
  showDialog(<AddBasicComponentModal />).catch(() => {});
};

const pagination = ref({
  current: 1,
  size: 999,
  total: 0,
});

const { data, loading, run } = useRequest(getToolCategory, {
  defaultParams: [
    {
      current: pagination.value.current,
      size: pagination.value.size,
      child: true,
    },
  ],
  formatResult: res => res.records,
});

const refresh = () => {
  run({
    current: pagination.value.current,
    size: pagination.value.size,
    child: true,
  });
};
</script>
