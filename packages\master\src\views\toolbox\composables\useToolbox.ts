/**
 * @file composables/useToolbox.ts
 * @description 工具箱组合式函数
 */

import { ref, computed } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { getToolCategory } from '@/master/apis/tools';
import type { ViewMode, ToolCategoryWithTools } from '../types';
import { DEFAULT_PAGINATION } from '../constants';

/**
 * 工具箱主要逻辑
 */
export function useToolbox() {
  // 视图模式
  const viewMode = ref<ViewMode>('card');

  // 分页配置
  const pagination = ref({
    current: DEFAULT_PAGINATION.CURRENT,
    size: DEFAULT_PAGINATION.SIZE,
    total: DEFAULT_PAGINATION.TOTAL,
  });

  // 数据请求
  const { data, loading, run } = useRequest(getToolCategory, {
    defaultParams: [
      {
        current: pagination.value.current,
        size: pagination.value.size,
        child: true,
      },
    ],
    formatResult: res => {
      // 将 ToolCategory[] 转换为 ToolCategoryWithTools[]
      return res.records.map(category => ({
        ...category,
        children: category.children as any,
      })) as ToolCategoryWithTools[];
    },
  });

  // 计算属性
  const hasData = computed(() => {
    return data.value && data.value.length > 0;
  });

  // 事件处理函数
  const handleViewModeChange = (mode: ViewMode) => {
    viewMode.value = mode;
  };

  const handleReload = () => {
    run({
      current: pagination.value.current,
      size: pagination.value.size,
      child: true,
    });
  };

  return {
    // 状态
    viewMode,
    pagination,
    data,
    loading,
    hasData,

    // 方法
    handleViewModeChange,
    handleReload,
    run,
  };
}

/**
 * 工具操作相关逻辑
 */
export function useToolActions() {
  const handleAddSoftwareComponent = async () => {
    const { showDialog } = await import('dialog-async');
    const { default: AddSoftwareComponentModal } = await import(
      '../components/AddSoftwareComponentModal.vue'
    );
    try {
      await showDialog(AddSoftwareComponentModal);
      return true;
    } catch {
      return false;
    }
  };

  const handleAddBasicComponent = async () => {
    const { showDialog } = await import('dialog-async');
    const { default: AddBasicComponentModal } = await import(
      '../components/AddBasicComponentModal.vue'
    );
    try {
      await showDialog(AddBasicComponentModal);
      return true;
    } catch {
      return false;
    }
  };

  return {
    handleAddSoftwareComponent,
    handleAddBasicComponent,
  };
}
