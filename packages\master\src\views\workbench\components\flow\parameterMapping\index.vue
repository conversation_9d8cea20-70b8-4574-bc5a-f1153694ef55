<template>
  <a-modal
    v-model:open="visible"
    cancel-text="取消"
    ok-text="确定"
    width="95%"
    :destroy-on-close="true"
    @cancel="visible = false"
    @ok="submit"
  >
    <template #title>
      <div class="modal-title-with-legend">
        <span>数据关联</span>
        <div class="parameter-legend">
          <div class="legend-item">
            <component :is="icons.INPUT" class="text-blue-400" />
            <span>输入参数</span>
          </div>
          <div class="legend-item">
            <component :is="icons.OUTPUT" class="text-orange-400" />
            <span>输出参数</span>
          </div>
        </div>
      </div>
    </template>
    <div class="parameter-mapping-container">
      <!-- 图形区域 -->
      <div class="graph-container">
        <div id="container" ref="containerRef"></div>
        <div class="toolbar">
          <a-space>
            <a-tooltip title="放大">
              <ZoomInOutlined class="toolbar-icon" @click="zoomIn" />
            </a-tooltip>
            <a-tooltip title="缩小">
              <ZoomOutOutlined class="toolbar-icon" @click="zoomOut" />
            </a-tooltip>
            <a-tooltip title="重置">
              <RedoOutlined class="toolbar-icon" @click="zoomReset" />
            </a-tooltip>
          </a-space>
        </div>
      </div>

      <!-- 表达式区域 -->
      <div class="expression-panel">
        <div class="panel-header">
          <span class="panel-title">表达式</span>
        </div>

        <!-- 运算符工具栏 -->
        <div class="operator-toolbar">
          <span class="operator-label">运算符：</span>
          <a-space>
            <a-button type="primary" size="small" @click="handleCalculate('+')">
              +
            </a-button>
            <a-button type="primary" size="small" @click="handleCalculate('-')">
              -
            </a-button>
            <a-button type="primary" size="small" @click="handleCalculate('*')">
              *
            </a-button>
            <a-button type="primary" size="small" @click="handleCalculate('/')">
              /
            </a-button>
          </a-space>
        </div>

        <!-- 映射列表 -->
        <div class="mapping-list">
          <div
            v-for="(item, index) in nodeData"
            :key="item.id"
            class="mapping-item"
          >
            <!-- 源参数 -->
            <div class="source-param" :title="item.fromName">
              <span class="param-name">{{ item.fromName }}</span>
            </div>

            <!-- 等号 -->
            <div class="equals-sign">=</div>

            <!-- 目标参数/表达式 -->
            <div class="target-expression">
              <a-mentions
                v-model:value="item.toName"
                class="expression-input"
                :loading="loading"
                :options="
                  pointList.map(r => ({ label: r.tagName, value: r.tagNo }))
                "
                not-found-content="无匹配项"
                placeholder="通过@符号选择点位，输入运算符进行计算"
                @focus="onFocus(item.id)"
              />
            </div>

            <!-- 删除按钮 -->
            <div class="delete-button">
              <a-button
                type="text"
                danger
                size="small"
                title="删除"
                @click="removeCell(item.id, index)"
              >
                <template #icon>
                  <RiCloseCircleLine />
                </template>
              </a-button>
            </div>
          </div>

          <!-- 空状态提示 -->
          <a-empty
            v-if="nodeData.length === 0"
            description="请在图形区域连接节点创建数据映射"
          />
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import editModelCtrl from './editModelCtrl';
import RiCloseCircleLine from '~icons/ri/close-circle-line';
import ZoomInOutlined from '~icons/ant-design/zoom-in-outlined';
import ZoomOutOutlined from '~icons/ant-design/zoom-out-outlined';
import RedoOutlined from '~icons/ant-design/redo-outlined';
import { useWorkflowGraph } from '../useWorkFlowGraph';
import { NODE_TYPE } from '../../../constants';
import icons from '@/master/views/toolbox/icons';

/**
 * 点位数据接口
 */
interface PointData {
  tagNo: string;
  tagName: string;
}

// 获取工作流上下文
const { workflow, workflowParams } = useWorkflowGraph();

// 状态变量
const visible = ref(false);
const loading = ref(false);
const containerRef = ref<HTMLElement | null>(null);
const pointList = ref<PointData[]>([]);
const focusId = ref('');

// 从控制器获取方法和状态
const {
  initNode,
  disposeGraph,
  nodeData,
  globalDataMap,
  removeCell,
  getListArr,
  zoomIn,
  zoomOut,
  zoomReset,
} = editModelCtrl();

/**
 * 初始化数据关联图
 */
const initDataAssociationGraph = async () => {
  if (!workflow.value) return;

  // 获取容器元素
  const container = document.getElementById('container');
  if (!container) return;

  try {
    loading.value = true;

    // 获取节点列表数据
    await getListArr(workflow.value!);

    // 初始化节点
    initNode(container, workflowParams.value.globalData as any[]);

    // 获取点位列表
    getPointList();
  } catch (error) {
    console.error('初始化数据关联图失败:', error);
  } finally {
    loading.value = false;
  }
};

/**
 * 监听弹窗可见性变化
 */
watch(
  () => visible.value,
  newVal => {
    if (newVal) {
      nextTick(initDataAssociationGraph);
    }
  }
);

/**
 * 提交数据关联配置
 */
const submit = () => {
  // 保存节点数据映射和全局数据映射
  workflowParams.value.nodeDataMap = nodeData.value;
  workflowParams.value.globalDataMap = globalDataMap.value;

  // 关闭弹窗
  visible.value = false;
};

/**
 * 获取点位列表
 */
const getPointList = () => {
  pointList.value = [];

  // 获取软件节点
  const nodes = workflow.value
    ?.getNodes()
    .filter(node => node.data.componentType === NODE_TYPE.SOFT);

  if (!nodes || nodes.length === 0) return;

  // 处理每个节点的输入和输出参数
  nodes.forEach(node => {
    try {
      // 解析输入参数
      const inputParams = JSON.parse(node.data.inputParams || '[]');
      const inputPoints = inputParams.map((param: { name1: string }) => ({
        tagNo: param.name1,
        tagName: '',
      }));

      // 解析输出参数
      const outputParams = JSON.parse(node.data.outputParams || '[]');
      const outputPoints = outputParams.map((param: { name1: string }) => ({
        tagNo: param.name1,
        tagName: '',
      }));

      // 合并点位列表
      pointList.value.push(...inputPoints, ...outputPoints);
    } catch (error) {
      console.error('解析节点参数失败:', error);
    }
  });
};

/**
 * 设置当前焦点项
 */
const onFocus = (id: string) => {
  focusId.value = id;
};

/**
 * 处理计算操作符
 */
const handleCalculate = (operator: string) => {
  if (!focusId.value) return;

  // 查找当前焦点项
  const item = nodeData.value.find(node => node.id === focusId.value);
  if (!item) return;

  // 处理表达式
  let expression = item.toName || '';

  // 如果表达式末尾已经有运算符，则替换它
  const lastChar = expression.charAt(expression.length - 1);
  if (['+', '-', '*', '/'].includes(lastChar)) {
    expression = expression.slice(0, -1);
  }

  // 添加新的运算符
  item.toName = expression + operator;
};

// 清理函数
onBeforeUnmount(() => {
  disposeGraph();
});

// 暴露组件属性
defineExpose({
  visible,
});
</script>
<style lang="less">
.parameter-mapping-container {
  display: flex;
  height: 650px;
  gap: 16px;
}

/* 图形区域样式 */
.graph-container {
  flex: 1;
  position: relative;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

#container {
  width: 100%;
  height: 100%;
}

.toolbar {
  position: absolute;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  padding: 8px 16px;
  border-radius: 20px;
  z-index: 10;
}

.toolbar-icon {
  font-size: 18px;
  cursor: pointer;
  color: #1890ff;
  padding: 4px;
  transition: all 0.3s;

  &:hover {
    color: #40a9ff;
    transform: scale(1.1);
  }
}

/* 表达式区域样式 */
.expression-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.panel-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.panel-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  position: relative;
  padding-left: 12px;

  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 16px;
    background-color: #1890ff;
    border-radius: 2px;
  }
}

.operator-toolbar {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
}

.operator-label {
  margin-right: 8px;
  color: #595959;
}

/* 映射列表样式 */
.mapping-list {
  flex: 1;
  padding: 12px 16px;
  overflow-y: auto;
}

.mapping-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dashed #f0f0f0;
}

.source-param {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #262626;
  font-size: 14px;
}

.equals-sign {
  margin: 0 8px;
  color: #8c8c8c;
  font-weight: bold;
}

.target-expression {
  flex: 2;
}

.expression-input {
  width: 100%;
}

.delete-button {
  margin-left: 8px;
}

.tag-name {
  margin-left: 4px;
  color: #8c8c8c;
  font-size: 12px;
}

/* 弹窗标题和图例样式 */
.modal-title-with-legend {
  display: flex;
  align-items: center;
}

.parameter-legend {
  display: flex;
  align-items: center;
  gap: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: normal;
  margin-left: 8px;
  color: #595959;
}
</style>
