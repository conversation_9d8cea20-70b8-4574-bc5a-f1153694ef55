<template>
  <div class="card-list-container">
    <div v-if="hasData" class="cards-grid">
      <Card
        v-for="category in dataSource"
        :key="category.sysDesComponentTypeId"
        :name="category.name"
        :children="category.children"
        @reload="handleReload"
      />
    </div>

    <a-empty v-else description="暂无工具组件数据" class="empty-placeholder">
      <template #image>
        <div class="empty-icon">
          <GridiconsFolder class="folder-icon" />
        </div>
      </template>
    </a-empty>
  </div>
</template>

<script setup lang="ts">
import type { ToolCategoryWithTools } from '../types';
import Card from './card.vue';
import GridiconsFolder from '~icons/gridicons/folder';

// 组件属性
const { dataSource } = defineProps<{
  dataSource?: ToolCategoryWithTools[];
}>();

// 事件定义
const emits = defineEmits<{
  reload: [];
}>();

// 计算属性
const hasData = computed(() => {
  return dataSource && dataSource.length > 0;
});

// 事件处理
const handleReload = () => {
  emits('reload');
};
</script>

<style lang="less" scoped>
.card-list-container {
  height: 100%;

  .cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 16px;
    padding: 8px;
  }

  .empty-placeholder {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 300px;

    .empty-icon {
      margin-bottom: 16px;

      .folder-icon {
        font-size: 64px;
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .cards-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style>
