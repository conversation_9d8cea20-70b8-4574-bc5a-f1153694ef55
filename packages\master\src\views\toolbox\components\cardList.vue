<template>
  <div v-if="dataSource?.length" class="grid grid-cols-3 gap-2">
    <Card
      v-for="item in dataSource"
      :key="item.sysDesComponentTypeId"
      :name="item.name"
      :children="item.children"
      @reload="handleReload"
    />
  </div>
  <a-empty v-else description="暂无数据" />
</template>

<script setup lang="ts">
import type { ToolCategory } from '@/master/types/tool';
import Card from './card.vue';

const { dataSource } = defineProps<{
  dataSource?: ToolCategory[];
}>();

const emits = defineEmits(['reload']);

const handleReload = () => {
  emits('reload');
};
</script>
