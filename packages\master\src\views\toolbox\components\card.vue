<template>
  <a-card :title="name" class="tool-category-card">
    <!-- 空状态 -->
    <a-empty
      v-if="!children?.length"
      :image="simpleImage"
      description="暂无组件"
      class="empty-state"
    />

    <!-- 工具网格 -->
    <div v-else class="tools-grid">
      <div
        v-for="(item, index) in children"
        :key="`${item.sysDesComponentId}-${index}`"
        class="tool-item"
        @click="handleToolClick(item)"
      >
        <!-- 工具图标 -->
        <div class="tool-icon">
          <Icon
            v-if="isObjectString(item.componentIcon)"
            :value="parsedIcon(item.componentIcon)"
          />
          <component
            v-else
            :is="getToolIcon(item.componentIcon)"
            class="icon-component"
          />
        </div>

        <!-- 工具名称 -->
        <p class="tool-name" :title="item.componentName">
          {{ item.componentName }}
        </p>

        <!-- 操作遮罩 -->
        <div class="tool-mask">
          <a-space size="small">
            <a-tooltip title="编辑">
              <AntDesignEditOutlined
                v-if="item.componentType === TOOL_TYPE.SOFT"
                class="action-btn edit-btn"
                @click.stop="handleEdit(item)"
              />
            </a-tooltip>

            <a-tooltip :title="item.enable ? '禁用' : '启用'">
              <a-switch
                :checked="!!item.enable"
                size="small"
                @click.stop
                @change="checked => handleToggleEnable(item, !!checked)"
              />
            </a-tooltip>

            <a-tooltip title="删除">
              <a-popconfirm
                title="确定删除该组件吗？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(item)"
                @click.stop
              >
                <DeleteOutlined class="action-btn delete-btn" />
              </a-popconfirm>
            </a-tooltip>
          </a-space>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import AntDesignEditOutlined from '~icons/ant-design/edit-outlined';
import DeleteOutlined from '~icons/ant-design/delete-outlined';
import { Empty, message } from 'ant-design-vue';
import { updateTool } from '@/master/apis/tools';
import icons from '@/master/views/toolbox/icons';
import type { Tool } from '@/master/types/tool';
import Icon from 'shared/components/Icon/index.vue';
import { isObjectString } from 'shared/utils';
import { ToolType } from '@/master/views/toolbox/workbench/constants';

// 常量
const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
const TOOL_TYPE = ToolType;

// 路由
const router = useRouter();

// 组件属性
const { name, children } = defineProps<{
  name: string;
  status?: string;
  children?: Tool[];
}>();

// 事件定义
const emits = defineEmits<{
  reload: [];
}>();

// 工具函数
const parsedIcon = (icon: string) => {
  try {
    return JSON.parse(icon);
  } catch {
    return null;
  }
};

const getToolIcon = (iconName: string) => {
  if (!iconName) return null;
  return icons[iconName.toUpperCase() as keyof typeof icons] || null;
};

// 事件处理函数
const handleToolClick = (item: Tool) => {
  // 可以在这里添加工具点击的默认行为
  console.log('Tool clicked:', item.componentName);
};

const handleEdit = (item: Tool) => {
  router.push({
    path: '/workbench',
    query: {
      sysDesComponentId: item.sysDesComponentId,
      professionalSoftwareId: item.desProfessionalSoftwareId,
    },
  });
};

const handleToggleEnable = async (item: Tool, checked: boolean) => {
  try {
    await updateTool({
      sysDesComponentId: item.sysDesComponentId,
      enable: checked ? 1 : 0,
    });
    message.success(checked ? '启用成功' : '禁用成功');
    emits('reload');
  } catch (error) {
    message.error('操作失败');
  }
};

const handleDelete = async (item: Tool) => {
  try {
    await updateTool({
      sysDesComponentId: item.sysDesComponentId,
      bsflag: 'Y',
    });
    message.success('删除成功');
    emits('reload');
  } catch (error) {
    message.error('删除失败');
  }
};
</script>

<style lang="less" scoped>
.tool-category-card {
  height: 100%;

  .empty-state {
    padding: 40px 0;
  }

  .tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
    padding: 4px;
  }

  .tool-item {
    position: relative;
    width: 100px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
    padding: 8px;

    &:hover {
      background-color: rgba(0, 0, 0, 0.02);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .tool-mask {
        opacity: 1;
      }
    }

    .tool-icon {
      width: 56px;
      height: 56px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;

      .icon-component {
        font-size: 48px;
        color: var(--primary-color);
      }
    }

    .tool-name {
      font-size: 12px;
      text-align: center;
      margin: 0;
      line-height: 1.2;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: rgba(0, 0, 0, 0.85);
    }

    .tool-mask {
      position: absolute;
      inset: 0;
      background: rgba(0, 0, 0, 0.6);
      border-radius: 8px;
      opacity: 0;
      transition: opacity 0.3s ease;
      display: flex;
      justify-content: center;
      align-items: center;
      backdrop-filter: blur(2px);

      .action-btn {
        font-size: 18px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }

        &.edit-btn {
          color: #1890ff;

          &:hover {
            color: #40a9ff;
          }
        }

        &.delete-btn {
          color: #ff4d4f;

          &:hover {
            color: #ff7875;
          }
        }
      }
    }
  }
}
</style>
