<template>
  <a-card :title="name">
    <a-empty
      v-if="children?.length == 0"
      :image="simpleImage"
      class="text-center"
    />
    <div
      class="grid overflow-y-auto gap-1"
      style="
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        height: 300px;
      "
    >
      <div
        v-for="(item, index) in children"
        :key="index"
        class="text-center cursor-pointer relative tool"
      >
        <div class="m-auto h-14 w-14">
          <icon
            v-if="isObjectString(item.componentIcon)"
            :value="parsedIcon(item.componentIcon)"
          />
          <component
            v-else
            :is="icons[item.componentIcon.toUpperCase() as keyof typeof icons]"
            class="text-5xl text-[var(--primary-color)]"
          />
        </div>
        <p class="text-ellipsis whitespace-nowrap overflow-hidden">
          {{ item.componentName }}
        </p>
        <div class="mask">
          <a-space>
            <AntDesignEditOutlined
              v-if="item.componentType === 2"
              class="cursor-pointer text-blue-500"
              title="编辑"
              @click="handleEdit(item)"
            />
            <a-switch size="small" title="启用" />
            <a-popconfirm
              title="确定删除吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(item)"
            >
              <DeleteOutlined title="删除" class="text-red-500" />
            </a-popconfirm>
          </a-space>
        </div>
      </div>
    </div>
  </a-card>
</template>

<script setup lang="ts">
import AntDesignEditOutlined from '~icons/ant-design/edit-outlined';
import DeleteOutlined from '~icons/ant-design/delete-outlined';
import { Empty, message } from 'ant-design-vue';
import { updateTool } from '@/master/apis/tools';
import icons from '@/master/views/toolbox/icons';
import type { Tool } from '@/master/types/tool';
import Icon from 'shared/components/Icon/index.vue';
import { isObjectString } from 'shared/utils';

const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;

const router = useRouter();

const { name, children } = defineProps<{
  name: string;
  status?: string;
  children?: Tool[];
}>();
const emits = defineEmits(['reload']);

const handleEdit = (item: Tool) => {
  router.push({
    path: '/workbench',
    query: {
      sysDesComponentId: item.sysDesComponentId,
      professionalSoftwareId: item.desProfessionalSoftwareId,
    },
  });
};

const parsedIcon = (icon: string) => {
  return JSON.parse(icon);
};

const handleDelete = async (item: any) => {
  try {
    await updateTool({
      sysDesComponentId: item.sysDesComponentId,
      bsflag: 'Y',
    });
    message.success('删除成功');
    emits('reload');
  } catch (e: any) {
    message.error('删除失败');
  }
};
</script>

<style lang="less" scoped>
.tool {
  width: 100px;
  height: 100px;
  &:hover .mask {
    opacity: 1;
  }
}

.mask {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 8px;
  opacity: 0;
  transition: 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 2px;
  font-size: 24px;
}
</style>
