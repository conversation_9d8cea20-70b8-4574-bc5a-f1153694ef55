<template>
  <a-modal
    v-model:open="dialog.visible"
    title="选择组件类型"
    :footer="null"
    centered
    @cancel="dialog.cancel"
  >
    <div class="software-container">
      <div v-for="item in modelStore.software" :key="item.softwareCode">
        <div class="software-item" @click="handleSoftwareClick(item)">
          <component
            v-if="icons[item.softwareName.toUpperCase() as keyof typeof icons]"
            :is="icons[item.softwareName.toUpperCase() as keyof typeof icons]"
            class="w-full h-full"
          />
        </div>
        <p class="text-xs whitespace-nowrap text-gray-500 font-bold mt-2">
          {{ item.softwareName }}
        </p>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
import useModelStore from '@/master/stores/model';
import { useDialog } from 'dialog-async';
import icons from '../icons';
import { SoftWare } from '@/master/types/software';

const modelStore = useModelStore();
const router = useRouter();
const dialog = useDialog();

const handleSoftwareClick = (item: SoftWare) => {
  dialog.cancel();
  router.push({
    path: '/toolbox/add',
    query: {
      professionalSoftwareId: item.desProfessionalSoftwareId,
    },
  });
};

onMounted(() => {
  if (modelStore.software.length === 0) {
    modelStore.initSoftware();
  }
});
</script>

<style lang="less">
.software-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, 50px);
  gap: 10px;
  padding: 12px;
}
.software-item {
  text-align: center;
  cursor: pointer;
  width: 50px;
  height: 50px;
  color: var(--primary-color);
  transition: color 0.3s;
  &:hover {
    color: var(--accent-200);
    & ~ p {
      color: var(--accent-200);
    }
  }
}
</style>
