<template>
  <a-modal
    v-model:open="dialog.visible"
    title="新增模型文件"
    width="50%"
    @cancel="dialog.cancel"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="模型名称" name="modelName">
        <a-input
          :value="current.modelName"
          disabled
          placeholder="请输入模型名称"
        />
      </a-form-item>
      <a-form-item label="专业软件" name="softwareCode">
        <a-select
          :value="current.desProfessionalSoftwareId"
          :options="
            modelStore.software.map((item: any) => ({
              label: item.softwareName,
              value: item.desProfessionalSoftwareId,
            }))
          "
          disabled
          placeholder="请选择"
        />
      </a-form-item>
      <a-form-item label="模型文件" name="modelPath" required>
        <a-upload
          :file-list="modelFileList"
          name="file"
          :max-count="1"
          :multiple="false"
          :action="modelFileAction"
          :before-upload="handleBeforeUpload"
        >
          <a-button>
            <upload-outlined></upload-outlined>
            点击上传
          </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item label="模型图片" name="modelImage">
        <a-upload
          :file-list="modelImageList"
          name="file"
          accept="image/*"
          :max-count="1"
          :multiple="false"
          :before-upload="handleImageBeforeUpload"
        >
          <a-button>
            <upload-outlined></upload-outlined>
            点击上传
          </a-button>
        </a-upload>
      </a-form-item>
      <a-form-item label="模型描述" name="remark">
        <a-textarea
          v-model:value="formState.remark"
          :rows="4"
          placeholder="请输入模型描述"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <div>
        <a-button @click="dialog.cancel">取消</a-button>
        <a-button type="primary" :loading @click="handldSubmit">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { message } from 'ant-design-vue';
import { useDialog } from 'dialog-async';
import useModelStore from '@/master/stores/model';
import type { SysDesModelAttachment } from '@/master/types/model-management';
import { ModelRecord } from '@/master/types/model';
import UploadOutlined from '~icons/ant-design/upload-outlined';
import {
  addModelAttachment,
  uploadModel,
  uploadModelImage,
} from '@/master/apis/model-management';

const { current } = defineProps<{
  current: ModelRecord;
}>();

const dialog = useDialog();
const loading = ref(false);

const modelFileList = ref<any[]>([]);
const modelImageList = ref<any[]>([]);

const modelStore = useModelStore();

const formRef = ref();

const formState = ref<Partial<SysDesModelAttachment>>({
  sysDesModelId: current.sysDesModelId,
});

const modelFileAction = (file: any) =>
  uploadModel({
    file,
    sysDesModelAttachmentId: formState.value.sysDesModelAttachmentId!,
    desProfessionalSoftwareId: current.desProfessionalSoftwareId,
    fileVersion: 'V1',
  });

const handleBeforeUpload = (file: any) => {
  modelFileList.value = [file];
  formState.value.modelPath = file.name;
  formState.value.modelName = file.name;
  return false;
};

const handleImageBeforeUpload = (file: any) => {
  modelImageList.value = [file];
  formState.value.modelImage = file.name;
  return false;
};
const handldSubmit = () => {
  formRef.value
    .validate()
    .then(async () => {
      loading.value = true;
      const { sysDesModelAttachmentId } = await addModelAttachment(
        formState.value
      );
      formState.value.sysDesModelAttachmentId = sysDesModelAttachmentId;
      await uploadModel({
        file: modelFileList.value[0],
        sysDesModelAttachmentId: formState.value.sysDesModelAttachmentId!,
        desProfessionalSoftwareId: current.desProfessionalSoftwareId,
        fileVersion: 'V1',
      });
      await uploadModelImage(
        modelImageList.value[0],
        formState.value.sysDesModelAttachmentId!
      );
      message.success('新建成功!');
      loading.value = false;
      dialog.submit();
    })
    .catch((error: any) => {
      message.error('error', error);
      loading.value = false;
    });
};
</script>
