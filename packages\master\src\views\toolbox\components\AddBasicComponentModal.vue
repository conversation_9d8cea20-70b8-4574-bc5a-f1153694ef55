<template>
  <a-modal
    v-model:open="dialog.visible"
    title="新增基础组件"
    centered
    @cancel="dialog.cancel"
    @ok="handleOk"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :label-col="{ style: 'width: 80px' }"
    >
      <a-form-item label="组件名称" name="componentName" required>
        <a-input
          v-model:value="formState.componentName"
          placeholder="请输入组件名称"
        />
      </a-form-item>
      <a-form-item label="图标" name="componentIcon">
        <IconMaker v-model:value="formState.componentIcon" />
      </a-form-item>
      <a-form-item label="组件描述" name="remarks">
        <a-textarea
          v-model:value="formState.remarks"
          placeholder="请输入组件描述"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts" setup>
import { ToolCreateParams } from '@/master/types/tool';
import { useDialog } from 'dialog-async';
import IconMaker from 'shared/components/IconMaker/index.vue';
import { FormInstance, message } from 'ant-design-vue';

const dialog = useDialog();
const formRef = useTemplateRef<FormInstance>('formRef');

const formState = ref<Partial<ToolCreateParams>>({
  sysDesComponentTypeId: 'bce0fe6d7bc62931fde6e4d6a187fb61',
});

const handleOk = async () => {
  try {
    await formRef.value?.validateFields();
    dialog.submit(formState.value);
  } catch {
    message.error('请填写完整信息');
  }
};
</script>
