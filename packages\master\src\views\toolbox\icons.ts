import PIPESIM from '~icons/my/pipeSim';
import GA<PERSON> from '~icons/my/Gap';
import HYSIS from '~icons/my/Hysis';
import LEDAFLOW from '~icons/my/LedaFlow';
import KSPIC<PERSON> from '~icons/my/KSpice';
import SEPSIM from '~icons/my/SEPSIM';
import TGNET from '~icons/my/TGNET';
import GROUP from '~icons/my/group';
import LOOP from '~icons/my/loop';
import CONDITION from '~icons/my/condition';
import DATA from '~icons/my/data';
import START from '~icons/my/start';
import END from '~icons/my/end';
import PLACEHOLDER from '~icons/my/placeholder';
import OUTPUT from '~icons/material-symbols/output';
import INPUT from '~icons/material-symbols/input';

const icons = {
  PIPESIM,
  'IPM GAP': GAP,
  GAP,
  HYSIS,
  LEDAFLOW,
  SEPSIM,
  TGNET,
  KSPIC<PERSON>,
  GROUP,
  LOOP,
  CONDITION,
  DATA,
  START,
  END,
  PLACEHOLDER,
  INPUT,
  OUTPUT,
};

export default new Proxy(icons, {
  get(target, prop: string) {
    return (
      target[prop] ||
      target[prop?.toUpperCase?.()] ||
      target[prop?.toLowerCase?.()]
    );
  },
});
